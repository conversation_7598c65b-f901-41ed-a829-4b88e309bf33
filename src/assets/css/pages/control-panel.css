header{
    background-color: #231a2b;
}

body{
    background-color: #06060a;
    overflow-y: hidden;
}

body p, h1, h2, h3, h4, h5, h6, span{
    color: silver;
}

nav{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

nav > div{
    display: flex;
    align-items: center;
}

#logomark{
    width:3rem;
}

#app_name{
    display: flex;
    align-items: center;
    justify-content: center;
    padding-inline: 1.6rem;
    background-image: url(../images/medieval-elements/frames/rectangle-frame-3-darkbrown.webp);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 120%;
    width: fit-content;
    max-height: 5vh;
    min-height: min-content;
}

#notepad_icon{
    width: 1.25rem;
}

#app_name_text{
    font-family: "MedievalSharp", cursive;
    font-size: x-large;
    color: #f2a60d;
    text-shadow: 0rem -0.1rem rgba(255, 255, 255, 0.699);
    filter: contrast(1.2);
    padding-inline: 0.2rem;
    white-space: nowrap;
}

#anvil_icon{
    width: 1.5rem;
}

.fa-circle-user{
    font-size: 2.5rem;
    color: silver;
}

main{
    display: flex;
}

aside{
    background-color: #1a0025;
    width: 20vw;
    height: 92vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

aside > div{
    display: flex;
    flex-direction: column;
}

#hamburguer{
    display: flex;
    justify-content: start;
}

#hamburguer > button{
    width: fit-content;
}

.fa-bars{
    color: #730000;
}

#pages{
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.page{
    display: flex;
    justify-content: center;
    align-items: center;
    color: #f2a60d;
}

#footer{
    padding-top: 6rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
}

#footer > p{
    font-size: small;
    text-align: center;
}

#container{
    width: 80vw;
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

#container > div{
    display: flex;
    width: 100%;
}

#info{
    justify-content: center;
}

.character{
    display: flex;
    flex-direction: column;
    padding-top: 1.5rem;
    gap: 1rem;
}

.character > img{
    width: 8rem;
}

.stats{
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: start;
    padding-top: 5rem;
}

form{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    gap: 5rem;
}

#task-forge form > div{
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

#task-forge .main-data, .secondary-data{
    background-color: #f2a60d90;
    border: solid 0.2rem #730000;
    border-radius: 0.6rem;
}

#task-forge .main-data{
    width: 100%;
}

#task-forge #task_name, #task_description{
    outline: none;
    padding: 0.2rem;
    color: #34004b;
}

#task-forge .task-name{
    display: flex;
    justify-content: space-between;
}

#task-forge .task-name > button{
    width: 20%;
    background-color: #1a0025;
    color: #ffffff;
}

#task-forge .secondary-data{
    width: 100%;
    height: 50%;
}

#task-forge .secondary-data{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
}

#task-forge .secondary-data > h3{
    text-shadow: #000000 -0.05rem 0.1rem;
}

#task-board h4{
    text-wrap: nowrap;
}

#task-board .fa-trash-can{
    color: red;
}

#task-board .fa-pen-to-square{
    color: blue;
}

#task-board .fa-circle-left, #task-board .fa-circle-right{
    color: #f2a60dbd;
}

#task-board #manager{
    height: 100%;
    display: flex;
    justify-content: space-evenly;
}

#task-board .tasks-status{
    overflow-y: scroll;
    width: 20rem;
    height: 90%;
    border: solid 0.2rem;
    border-radius: 0.6rem;
}

#task-board .field{
    padding-block: 0.5rem;
}

#task-board .task, #task-board .task-content > .always-visible{
    display: flex;
}

#task-board .task{
    padding: 0.5rem;
    justify-content: space-around;
}

#task-board .task-content{
    display: flex;
    flex-direction: column;
}

#task-board .task-content > .always-visible{
    gap: 0.5rem;
}

#task-board .field{
    display: flex;
    justify-content: center;
}

/* Modal Styles */
.modal {
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: #fefefe;
}

.close-button {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
}

.close-button:hover, .close-button:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}