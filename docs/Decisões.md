# Decisões Fundamentais e Planejamento Estratégico do Tasksmith

Este documento detalha o processo de planejamento estratégico e as decisões fundamentais tomadas durante o desenvolvimento do Tasksmith, abrangendo aspectos de arquitetura, funcionalidades, priorização e roadmap.

## 1. Processo de Planejamento Estratégico

O planejamento estratégico do Tasksmith foi guiado por uma abordagem **iterativa e incremental**, com foco principal na entrega de um **Produ<PERSON> (MVP)** robusto e funcional para o Trabalho de Conclusão de Curso (TCC). A metodologia adotada enfatizou o **aprendizado prático** e a **resolução de problemas**, com cada etapa de implementação de funcionalidade sendo acompanhada de:

* **Registro de Decisões:** Documentação das escolhas técnicas e de design.
* **Identificação de Desafios:** Análise dos obstáculos encontrados durante o desenvolvimento.
* **Soluções Implementadas:** Detalhamento de como os desafios foram superados, incluindo pesquisa e aquisição de novos conhecimentos.
* **Reflexão:** Avaliação do aprendizado em cada etapa e sua contribuição para o conhecimento geral do desenvolvedor.

Este processo garantiu que o projeto não fosse apenas a criação de um produto, mas também um relato detalhado da jornada de desenvolvimento e aprendizado.

## 2. Decisões Fundamentais Tomadas

As seguintes decisões foram cruciais para moldar o Tasksmith e seu desenvolvimento:

### 2.1. Arquitetura Monolítica

* **Justificativa:** A escolha de uma arquitetura monolítica foi feita para **otimizar o desenvolvimento e a entrega do MVP** dentro do prazo do TCC. Esta abordagem simplifica a gestão do projeto, a depuração e a implantação inicial, permitindo um foco maior na implementação das funcionalidades essenciais. Além disso, facilita a compreensão dos fundamentos de uma aplicação web completa sem a complexidade adicional de múltiplos serviços.
* **Alternativas Consideradas:** Arquiteturas baseadas em microsserviços foram consideradas, mas descartadas para o escopo do MVP devido à sua maior complexidade de desenvolvimento, orquestração e implantação, o que poderia desviar o foco do objetivo principal do TCC.
* **Impacto Esperado:** Desenvolvimento ágil, menor curva de aprendizado inicial, facilidade de manutenção e implantação para o MVP.
* **Implementação Realizada:** A estrutura de diretórios foi estabelecida com sucesso: `src/` (código-fonte), `public/` (ponto de entrada e assets), `database/` (scripts de banco de dados) e `config/` (configurações), confirmando a base da arquitetura monolítica.

### 2.2. Tecnologias "Puras" (PHP puro, JavaScript vanilla, Tailwind CSS)

* **Justificativa:** A decisão de utilizar PHP puro, JavaScript vanilla e Tailwind CSS visa demonstrar a capacidade de construir uma aplicação web robusta e funcional sem a dependência excessiva de frameworks complexos. Esta escolha promove um **aprendizado aprofundado das linguagens e tecnologias base**, permitindo um controle mais granular sobre o código e uma compreensão mais clara dos mecanismos internos da aplicação. Adicionalmente, a **curva de aprendizado menor** dessas tecnologias contribui para a otimização do tempo de entrega do projeto.
* **Alternativas Consideradas:** Frameworks populares como Laravel (PHP), React/Vue/Angular (JavaScript) e Bootstrap/Materialize (CSS) foram consideradas. No entanto, a prioridade foi dada à compreensão dos fundamentos e à minimização de abstrações para o contexto do TCC.
* **Impacto Esperado:** Maior controle sobre o código, otimização potencial de performance para o MVP, e um conhecimento mais sólido das tecnologias subjacentes.
* **Implementação Realizada:** O Tailwind CSS foi configurado e a base do sistema de roteamento em PHP puro foi criada. A estrutura para `Views`, `Controllers` e `Services` está no lugar, mas a lógica de negócio dentro dos `Services` ainda não foi implementada, significando que o controle total sobre o fluxo da aplicação está estruturado, mas não funcional.

### 2.3. Foco na Gamificação com Tema RPG Medieval

* **Justificativa:** O cerne do Tasksmith é transformar a gestão de tarefas em uma **experiência envolvente e motivadora**. A aplicação de elementos de RPG medieval (XP, Ouro, Níveis, Customização de Personagem, Mapa de Jornada) foi escolhida para criar um ambiente lúdico que incentive a conclusão de tarefas e o engajamento do usuário. Este tema é amplamente reconhecido e oferece um vasto campo para a criatividade no design. Além disso, a escolha do tema fantasia medieval é motivada pela **paixão pessoal**, o que proporciona um **maior engajamento** e, consequentemente, uma **dedicação aprimorada** ao desenvolvimento do projeto.
* **Alternativas Consideradas:** Outros temas de gamificação (ex: ficção científica, esportes) ou abordagens mais abstratas foram possíveis, mas o tema medieval foi selecionado por sua riqueza visual e narrativa, que se alinha bem com a proposta de transformar tarefas em "aventuras".
* **Impacto Esperado:** Aumento significativo da motivação e produtividade do usuário, diferenciação do Tasksmith no mercado de aplicativos de gestão de tarefas. Embora um nicho possa ter um alcance inicial mais restrito, a qualidade da execução tem o potencial de fomentar uma **comunidade robusta e engajada**.

### 2.4. Definição do Escopo do Produto Mínimo Viável (MVP)

* **Justificativa:** Para garantir a conclusão do projeto dentro do prazo do TCC, foi essencial definir um escopo claro e limitado para o MVP. As funcionalidades selecionadas (Página Inicial, Autenticação, Painel do Usuário, Gestão de Tarefas CRUD, Sistema de XP e Níveis, Política de Penalidades) representam o conjunto mínimo necessário para demonstrar o conceito central de gamificação e produtividade.
* **Alternativas Consideradas:** Funcionalidades mais avançadas, como WebSockets para notificações em tempo real, sistemas de clãs, ou uma variedade maior de itens e customizações, foram identificadas como "Trabalhos Futuros" ou "Oportunidades Futuras", mas conscientemente excluídas do MVP para evitar a diluição do foco e o atraso na entrega.
* **Impacto Esperado:** Entrega de um produto funcional e demonstrável dentro do cronograma, validação dos princípios de gamificação aplicados.

### 2.5. Implementação de Sistema de Roteamento Personalizado

* **Justificativa:** A decisão de implementar um sistema de roteamento próprio ao invés de utilizar frameworks existentes foi tomada para **demonstrar compreensão dos mecanismos de roteamento web** e manter o controle total sobre o fluxo da aplicação. Esta abordagem permite um aprendizado mais aprofundado sobre como as requisições HTTP são processadas e direcionadas.
* **Alternativas Consideradas:** Frameworks como Laravel ou Slim PHP foram considerados, mas descartados para manter a filosofia de "tecnologias puras" e maximizar o aprendizado.
* **Impacto Esperado:** Maior compreensão dos fundamentos web, controle total sobre o roteamento, e flexibilidade para implementações futuras.
* **Implementação Realizada:** Foi desenvolvido um sistema de roteamento em PHP puro que evoluiu de um mapeamento simples para uma classe `Routes.php` robusta, suportando diferentes métodos HTTP por rota e despachando para `Controllers` específicos. O uso de `.htaccess` foi mantido para garantir URLs amigáveis.

### 2.6. Evolução do Front Controller e Organização dos Controllers

* **Justificativa:** A decisão de evoluir o Front Controller e refinar a organização dos Controllers surgiu da necessidade de aprimorar a separação de responsabilidades e a capacidade de processar lógica de negócios antes da renderização das views. Inicialmente, o roteador mapeava URLs diretamente para views, o que gerava acoplamento e limitava a complexidade da lógica. A evolução visa uma arquitetura MVC mais completa, onde o Front Controller instancia controladores e chama seus métodos.
* **Abordagens Consideradas:**
* **Controllers Baseados em Modelos vs. Funcionalidades:** Foi discutida a transição de uma abordagem puramente baseada em modelos para uma abordagem híbrida com foco em funcionalidades (ex: `AuthController`, `TaskController`, `GameController`). Esta escolha visa equilibrar a coesão funcional, o tamanho gerenciável dos arquivos e a responsabilidade única de cada controller.
* **Despacho Dinâmico:** A necessidade de converter strings (ex: `'HomeController@index'`) em chamadas de classe e método reais foi abordada, com a exploração de técnicas como `call_user_func` para flexibilidade.
* **Impacto Esperado:** Maior separação de responsabilidades, melhor processamento de formulários e preparação de dados para views, maior flexibilidade para implementar lógica de negócios complexa, e preparação para a escalabilidade futura do projeto.
* **Implementação Realizada:** O Front Controller (`public/index.php`) foi completamente refatorado. Ele agora instancia dinamicamente o `Controller` e invoca a `action` (método) apropriada com base na rota e no método HTTP da requisição (`GET`, `POST`, etc.). A estrutura de `Controllers` foi reorganizada por funcionalidade (`AuthController`, `HomeController`, `GameController`), abandonando a estrutura anterior que era mais focada em modelos.

### 2.7. Refatoração do Método `select` e Implementação dos Métodos CRUD em `QueryBuilder.php`

* **Justificativa:** A refatoração do método `select` foi motivada pela necessidade de aprimorar a **segurança** (prevenção de SQL Injection), a **clareza do código** e o **tratamento de erros** em uma implementação inicial feita pelo usuário. O objetivo era aplicar as melhores práticas sem descaracterizar a lógica original. A implementação dos métodos `insert`, `update` e `delete`, juntamente com a criação de uma função `execute` privada, foram passos importantes para centralizar a lógica de persistência de dados e expandir as funcionalidades de CRUD.
* **Decisões Tomadas:**
  * **Adoção de Prepared Statements:** Priorização do uso de `prepare()` e `execute()` com bindings para todas as variáveis que poderiam ser injetadas na query (especialmente em cláusulas `WHERE` e `LIKE`), garantindo a segurança.
  * **Tratamento de Erros Programático:** Substituição do retorno de strings de erro por valores booleanos (`false`) em caso de falha de validação ou `PDOException`, facilitando o tratamento de erros pelo código chamador. Erros de execução são logados internamente via `error_log()`.
  * **Simplificação da Lógica e Concisão:** O código foi iterativamente simplificado, removendo comentários excessivos e otimizando a construção da query para melhorar a legibilidade, atendendo ao feedback do usuário.
  * **Centralização da Execução de Queries:** Criação de um método `private function execute()` para encapsular a lógica de preparação e execução de queries, promovendo a reutilização de código e a consistência no tratamento de erros.
  * **Implementação dos Métodos `insert`, `update` e `delete`:** Desenvolvimento de métodos para inserção, atualização e exclusão de dados, utilizando Prepared Statements e a função `execute` centralizada.
  * **Refinamento dos Métodos `update` e `delete`:** Os métodos `update` e `delete` foram aprimorados para aceitar parâmetros de condição (`$condition_values`), permitindo o uso seguro de *prepared statements* em cláusulas `WHERE` e prevenindo SQL Injection.
  * **Injeção de Dependência:** O construtor da classe foi modificado para receber a conexão PDO como um parâmetro (injeção de dependência), em vez de instanciá-la internamente. Isso desacopla a classe da configuração de conexão e facilita os testes.
* **Impacto Esperado:** Maior segurança da aplicação, código mais robusto e fácil de manter, e um aprendizado aprofundado para o usuário sobre as melhores práticas de interação com banco de dados em PHP.
* **Implementação Realizada:** O `QueryBuilder.php` foi refatorado com sucesso para usar *prepared statements*, injeção de dependência e um método `execute` centralizado. Todos os métodos CRUD (`db_select`, `db_insert`, `db_update`, `db_delete`) foram implementados seguindo essas boas práticas, fornecendo uma camada de acesso a dados segura e robusta. A integração desta camada com a lógica de negócio nos `Services` é o próximo passo.

### 2.8. Metodologia de Desenvolvimento Iterativa e Incremental com Ênfase na Documentação do Processo de Aprendizado

* **Justificativa:** Esta metodologia foi adotada para que o TCC não fosse apenas a descrição de um produto final, mas também um **relato da jornada de aprendizado e resolução de problemas**. Ao documentar decisões, desafios e soluções em cada etapa, o projeto ganha um valor acadêmico adicional, servindo como um estudo de caso prático de desenvolvimento de software.
* **Impacto Esperado:** Documentação rica em insights técnicos e pedagógicos, fortalecimento do caráter de pesquisa e aprendizado do TCC.
* **Implementação Realizada:** Cada commit foi documentado com mensagens detalhadas explicando as mudanças implementadas, criando um histórico completo da evolução do projeto e das decisões tomadas.

### 2.9. Adoção de Variáveis de Ambiente com `phpdotenv`

* **Justificativa:** Para aumentar a segurança e a portabilidade do projeto, foi adotada a biblioteca `vlucas/phpdotenv`. Esta decisão permite que configurações sensíveis, como credenciais de banco de dados, sejam armazenadas em um arquivo `.env` que não é versionado no Git. Isso elimina a necessidade de ter dados sigilosos diretamente no código (hardcoding), facilitando a configuração do ambiente em diferentes máquinas (desenvolvimento, produção) e protegendo as credenciais.
* **Alternativas Consideradas:** Manter as configurações em um arquivo PHP (`config/settings.php`) foi a abordagem inicial, mas foi descartada por ser menos segura e flexível.
* **Impacto Esperado:** Maior segurança, facilidade de configuração em novos ambientes, e alinhamento com as melhores práticas de desenvolvimento de aplicações web modernas.
* **Implementação Realizada:** A biblioteca `vlucas/phpdotenv` foi instalada via Composer. O script `database/db_script.php` (e outros pontos de entrada) agora carrega as variáveis do arquivo `.env`, que é ignorado pelo Git, garantindo que as credenciais do banco de dados (`DB_HOST`, `DB_USER`, etc.) estejam seguras e fora do controle de versão.

### 2.10. Reavaliação do SGBD: Migração para MySQL

* **Justificativa:** Embora a adoção de variáveis de ambiente tenha tornado a aplicação agnóstica ao SGBD, foi tomada a decisão de padronizar o desenvolvimento e a documentação em torno do **MySQL**. Os principais motivos para essa escolha são a **vasta quantidade de recursos de aprendizado** (tutoriais, fóruns, documentação) disponíveis para a combinação PHP e MySQL, o que acelera a resolução de problemas, e a **praticidade oferecida por ambientes de desenvolvimento local como XAMPP/LAMPP**, que já vêm com MySQL pré-configurado e otimizado.
* **Alternativas Consideradas:** O SQLite foi a opção inicial devido à sua simplicidade (um único arquivo, sem necessidade de servidor). No entanto, para o propósito de um TCC que visa refletir práticas de mercado, o MySQL foi considerado mais representativo e robusto.
* **Impacto Esperado:** Maior agilidade no desenvolvimento, facilitada pelo amplo suporte da comunidade. Um ambiente de desenvolvimento mais próximo das configurações de hospedagem de produção, e maior facilidade para depurar e otimizar consultas SQL específicas do MySQL.
* **Implementação Realizada:** O script de criação de banco de dados (`database/db_script.php`) foi revisado para garantir total compatibilidade com a sintaxe do MySQL.

### 2.11. Organização da Lógica de Negócio e Persistência de Dados

* **Justificativa:** A decisão de refinar a organização da lógica de negócio e da persistência de dados visa equilibrar a simplicidade do projeto com a manutenção de uma boa separação de responsabilidades. Em vez de introduzir uma camada de Repositórios explícita, optou-se por consolidar a lógica de persistência diretamente nos Serviços, mantendo o projeto mais enxuto e alinhado com a filosofia de "PHP puro" e "simplicidade". A criação de um diretório dedicado para ferramentas de banco de dados de baixo nível (Query Builder) garante uma organização lógica para componentes reutilizáveis.
* **Abordagens Consideradas:**
  * **Camada de Repositórios Separada:** Considerada inicialmente para uma separação mais rigorosa da persistência, mas descartada para o escopo atual do MVP devido à preferência por manter a estrutura mais simples e evitar a criação de novos diretórios.
  * **Lógica de Persistência em Models:** Descartada para evitar "modelos gordos" e acoplamento forte das entidades com a camada de dados, mantendo os Models focados apenas na representação dos dados.
* **Impacto Esperado:** Manutenção da simplicidade do projeto, organização clara da lógica de negócio nos Serviços, e agrupamento de ferramentas de banco de dados em um local dedicado (`src/Db/`), facilitando a manutenção e a compreensão do fluxo de dados.
* **Implementação Atual:**
  * **Modelos (`src/Models/`):** A estrutura de pastas existe, mas os modelos ainda precisam ser criados. Eles servirão como DTOs (Data Transfer Objects).
  * **Serviços (`src/Services/`):** Os arquivos de serviço (ex: `UserService.php`) foram criados, mas estão vazios. É aqui que a lógica de negócio e a interação com o `QueryBuilder` irão residir.
  * **Banco de Dados (`src/Database/`):** O diretório foi criado e agora contém a configuração de conexão com o RedBeanPHP, centralizando a lógica de acesso a dados de baixo nível.

  ### 2.12. Refatoração Completa da Base de Código e Estrutura de Arquivos com Auxílio de IA

  * **Justificativa:** Devido ao aumento desnecessário da complexidade da construção do projeto ao longo do tempo, resultando em uma codebase desorganizada, com "gambiarras" e propensa a erros, foi tomada a decisão de realizar uma refatoração completa da base de código e da estrutura de arquivos. Esta refatoração foi auxiliada por uma inteligência artificial para otimizar o tempo e garantir a aplicação de melhores práticas. O objetivo principal foi simplificar o projeto, reduzir o boilerplate e melhorar a manutenibilidade, mantendo os Models e Views como as estruturas principais.

  * **Principais Alterações:**
    * **Adoção do ORM RedBeanPHP:** O `QueryBuilder` manual foi completamente removido e substituído pelo RedBeanPHP. Esta mudança simplificou drasticamente as operações de banco de dados, permitindo uma interação mais direta e intuitiva com a persistência.
    * **Modelos Active Record:** As classes `User` e `Task` foram refatoradas para incorporar sua própria lógica de persistência, seguindo o padrão Active Record do RedBeanPHP. Isso eliminou a necessidade de uma camada de serviço separada para operações CRUD (Create, Read, Update, Delete), reduzindo o número de arquivos e a complexidade.
    * **Simplificação da Estrutura de Diretórios:** O diretório `src/Services` foi limpo, e a lógica de negócios foi consolidada nos modelos e no novo `GameService`. A estrutura de diretórios do banco de dados foi unificada em `src/Database/`, removendo redundâncias.
    * **Controladores Enxutos:** Os controladores `AuthController` e `GameController` foram refatorados para interagir diretamente com os modelos, tornando-os mais simples, mais focados na orquestração das requisições e menos acoplados a camadas intermediárias.
    * **Tratamento de Erros Centralizado:** Um mecanismo robusto de tratamento de exceções foi implementado no `index.php` para capturar erros de forma centralizada e retornar respostas HTTP apropriadas. Novas exceções personalizadas (`ValidationException`, `AuthenticationException`, `AuthorizationException`) foram criadas em `src/Exceptions/` para um controle de erro mais granular.
    * **Gamificação:** A base para a lógica de gamificação foi estabelecida com a criação do modelo `Character` e a expansão do `GameService`, que agora lida com a conclusão de tarefas e a aplicação de recompensas aos personagens dos usuários.

  * **Impacto Esperado:** O projeto agora é significativamente mais organizado, mais fácil de entender e manter, e menos propenso a erros. A simplicidade da arquitetura e a adoção de um ORM moderno contribuem para um desenvolvimento mais ágil e uma base de código mais limpa.

  ## 3. Abrangência dos Aspectos

### 3.1. Arquitetura

A arquitetura do Tasksmith é monolítica, com uma clara separação lógica entre o frontend e o backend.

* **Frontend:** Desenvolvido com HTML, CSS (utilizando Tailwind CSS para utilitários e estilização rápida) e JavaScript vanilla para interatividade. As views são renderizadas no servidor e complementadas com lógica no cliente.
* **Backend:** Implementado em PHP puro, responsável pela lógica de negócios, manipulação de dados e interação com o banco de dados. Inclui módulos para autenticação, gestão de tarefas e sistema de XP/Níveis.
* **Banco de Dados:** Utilizado para persistir todos os dados da aplicação, incluindo usuários, tarefas, itens, inventário e notificações.

### 3.2. Funcionalidades

O MVP do Tasksmith abrange as seguintes funcionalidades principais:

* **Autenticação e Autorização:** Login e cadastro de usuários.
* **Gestão de Tarefas (CRUD):** Criação, leitura, atualização e exclusão de tarefas, incluindo suporte a marcação de conclusão.
* **Sistema de XP e Níveis:** Geração de pontos de experiência e "Ouro" ao concluir tarefas, com progressão de nível do personagem.
* **Política de Penalidades:** Dedução de XP ou Ouro para tarefas não cumpridas no prazo.

### 3.3. Priorização e Roadmap

A priorização foi focada na entrega do MVP para o TCC, garantindo que as funcionalidades essenciais de gamificação e produtividade estivessem presentes e operacionais. O roadmap implícito no projeto prevê:

* **Fase 1 (MVP - TCC):** Implementação das funcionalidades listadas acima, com foco na estabilidade e demonstração do conceito.
* **Fase 2 (Trabalhos Futuros):** Expansão das funcionalidades, como aprimoramento das notificações (potencialmente com WebSockets), adição de mais tipos de itens e customizações, implementação de sistemas sociais (ex: amigos, clãs), e aprofundamento das mecânicas de jogo.

O desenvolvimento seguirá um ciclo iterativo, permitindo a adição de novas funcionalidades e melhorias contínuas após a conclusão do MVP.
