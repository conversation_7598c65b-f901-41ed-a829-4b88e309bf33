<?php

namespace App\Services;

use App\Models\Character;
use App\Models\Task;

class GameService {
    public static function calculateRewards(int $difficulty): array {
        return match ($difficulty) {
            1 => ['xp' => 10, 'gold_amount' => 5],
            2 => ['xp' => 15, 'gold_amount' => 15],
            3 => ['xp' => 25, 'gold_amount' => 30],
            default => ['xp' => 0, 'gold_amount' => 0],
        };
    }

    public static function completeTask(int $taskId, int $userId) {
        $taskModel = new Task();
        $task = $taskModel->findById($taskId);

        if (!$task || $task->user_id !== $userId) {
            // Lançar exceção de autorização
            return;
        }

        // Marcar a tarefa como concluída
        $taskModel->updateStatus($taskId, "finished");

        // Aplicar recompensas
        $character = (new Character())->findByUserId($userId);

        if ($character) {
            // Lógica de recompensas movida para o serviço para garantir a persistência
            $character->xp += $task->xp_reward;
            $character->gold_amount += $task->gold_reward;

            // Lógica de Level Up
            $xp_needed = match ((int)$character->level) {
                1 => 100,
                2 => 150,
                default => 100 + ((int)$character->level - 1) * 50,
            };

            if ($character->xp >= $xp_needed) {
                $character->xp -= $xp_needed;
                $character->level += 1;
            }

            // Lógica de Ganho de Vida
            if ($character->hp < 3 && $character->gold_amount >= 100) {
                $character->hp += 1;
                $character->gold_amount -= 100;
            } else if ($character->hp >= 3 && $character->gold_amount > 99) {
                $character->gold_amount = 99;
            }

            \RedBeanPHP\R::store($character);
        }
    }

    public static function createCharacter(int $userId, string $characterName) {
        Character::create($userId, $characterName);
    }
}
